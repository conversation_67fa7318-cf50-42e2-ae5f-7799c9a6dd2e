.PHONY: help install dev run test test-auto test-ci test-watch lint format security docs clean start-services stop-services services-status restart-services docker-up docker-down traefik-start traefik-stop traefik-logs traefik-status traefik-rebuild test-traefik test-traefik-health test-behave test-behave-tags db-upgrade db-reset db-seed

# Variables
PROJECT_NAME := cso_platform
PYTHON := python3.11
POETRY := poetry
NIX_SHELL := nix-shell --run

help: ## Show this help message
	@echo "🎯 CSO Platform - Quantitative Cybersecurity Decision Support System"
	@echo "=================================================================="
	@echo ""
	@echo "📋 Available Commands:"
	@echo ""
	@echo "🚀 Development:"
	@awk 'BEGIN {FS = ":.*?## "} /^(dev|run|start-services|stop-services|services-status|restart-services):.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "🧪 Testing:"
	@awk 'BEGIN {FS = ":.*?## "} /^(test|test-auto|test-unit|test-integration|test-e2e|test-cov|test-fast|test-ci|test-watch|test-specific):.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "🔍 Code Quality:"
	@awk 'BEGIN {FS = ":.*?## "} /^(lint|format|security):.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "🗄️ Database:"
	@awk 'BEGIN {FS = ":.*?## "} /^(db-upgrade|db-downgrade|db-reset|db-seed|db-history|db-current):.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "📚 Documentation:"
	@awk 'BEGIN {FS = ":.*?## "} /^(docs|docs-serve|docs-clean):.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "🐳 Docker & Deployment:"
	@awk 'BEGIN {FS = ":.*?## "} /^(docker-up|docker-down|traefik-start|traefik-stop|traefik-status):.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "🧹 Maintenance:"
	@awk 'BEGIN {FS = ":.*?## "} /^(clean|install):.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "💡 Quick Start:"
	@echo "  1. make start-services    # Start PostgreSQL and Redis"
	@echo "  2. make db-upgrade        # Run database migrations"
	@echo "  3. make test-auto         # Run automated test suite"
	@echo "  4. make dev               # Start development server"
	@echo ""
	@echo "🎯 For automated testing: make test-auto"

install: ## Install project dependencies
	$(POETRY) install

# Service Management
start-services: ## Start PostgreSQL and Redis services
	@echo "🚀 Starting CSO Platform services..."
	@$(NIX_SHELL) "services_start"

stop-services: ## Stop PostgreSQL and Redis services
	@echo "🛑 Stopping CSO Platform services..."
	@$(NIX_SHELL) "services_stop"

services-status: ## Check status of all services
	@echo "📊 CSO Platform Service Status:"
	@$(NIX_SHELL) "services_status"

restart-services: ## Restart all services
	@echo "🔄 Restarting CSO Platform services..."
	@$(NIX_SHELL) "services_stop && sleep 2 && services_start"

dev: ## Run development server
	@echo "🚀 Starting CSO Platform development server..."
	@$(NIX_SHELL) "uvicorn src.cso_platform.main:app --reload --host 0.0.0.0 --port 8000"

run: ## Run production server
	@echo "🚀 Starting CSO Platform production server..."
	@$(NIX_SHELL) "uvicorn src.cso_platform.main:app --host 0.0.0.0 --port 8000"

# Testing Commands
test: ## Run all tests with coverage
	@echo "🧪 Running CSO Platform test suite..."
	@$(NIX_SHELL) "python -m pytest tests/ -v --cov=src --cov-report=term-missing"

test-unit: ## Run unit tests only
	@echo "🧪 Running unit tests..."
	@$(NIX_SHELL) "python -m pytest tests/unit/ -v"

test-integration: ## Run integration tests only
	@echo "🧪 Running integration tests..."
	@$(NIX_SHELL) "python -m pytest tests/integration/ -v"

test-e2e: ## Run end-to-end tests
	@echo "🧪 Running end-to-end tests..."
	@$(NIX_SHELL) "behave tests/e2e/features/"

test-cov: ## Run tests with detailed coverage report
	@echo "🧪 Running tests with coverage analysis..."
	@$(NIX_SHELL) "python -m pytest tests/ --cov=src --cov-report=html --cov-report=term-missing --cov-report=xml"

test-fast: ## Run tests without coverage (faster)
	@echo "🧪 Running fast test suite..."
	@$(NIX_SHELL) "python -m pytest tests/ -v --tb=short"

test-specific: ## Run specific test file (usage: make test-specific FILE=test_enhanced_risk_service.py)
	@echo "🧪 Running specific test: $(FILE)"
	@$(NIX_SHELL) "python -m pytest tests/unit/$(FILE) -v"

# Automated Testing Pipeline
test-auto: ## Start services if needed and run full test suite
	@echo "🤖 CSO Platform Automated Testing Pipeline"
	@echo "=========================================="
	@echo "1. Checking service status..."
	@$(NIX_SHELL) "services_status" || true
	@echo ""
	@echo "2. Starting services if not running..."
	@$(NIX_SHELL) "services_start" || true
	@echo ""
	@echo "3. Waiting for services to be ready..."
	@sleep 3
	@echo ""
	@echo "4. Running comprehensive test suite..."
	@$(NIX_SHELL) "python -m pytest tests/ -v --cov=src --cov-report=term-missing --cov-report=html"
	@echo ""
	@echo "✅ Automated testing pipeline completed!"
	@echo "📊 Coverage report available in htmlcov/index.html"

test-ci: ## CI/CD optimized test run
	@echo "🚀 Running CI/CD test suite..."
	@$(NIX_SHELL) "python -m pytest tests/ --cov=src --cov-report=xml --cov-report=term --junitxml=test-results.xml"

test-watch: ## Watch for file changes and run tests automatically
	@echo "👀 Watching for changes and running tests..."
	@$(NIX_SHELL) "ptw tests/ src/ --runner 'python -m pytest tests/unit/ -v'"

lint: ## Run linting checks
	@echo "🔍 Running code quality checks..."
	@$(NIX_SHELL) "ruff check src tests"
	@$(NIX_SHELL) "mypy src"

format: ## Format code
	@echo "🎨 Formatting code..."
	@$(NIX_SHELL) "ruff format src tests"
	@$(NIX_SHELL) "ruff check --fix src tests"

security: ## Run security checks
	@echo "🔒 Running security analysis..."
	@$(NIX_SHELL) "bandit -r src/"

docs: ## Build documentation
	@echo "📚 Building documentation..."
	@cd docs && $(NIX_SHELL) "make html"

docs-serve: ## Serve documentation locally
	@echo "📚 Serving documentation at http://localhost:8080"
	@cd docs && $(NIX_SHELL) "python -m http.server -d build/html 8080"

docs-clean: ## Clean documentation build
	@echo "🧹 Cleaning documentation build..."
	@cd docs && $(NIX_SHELL) "make clean"

docs-dev: ## Start development documentation server
	./scripts/dev-docs.sh

docs-deploy: ## Deploy documentation to GitHub Pages
	./scripts/deploy-docs.sh

setup-github-pages: ## Setup GitHub Pages via API
	./scripts/setup-github-pages.sh

# Nix-specific commands
install-nix: ## Install Nix package manager and direnv
	./scripts/install-nix.sh

nix-shell: ## Enter Nix development shell
	nix-shell

nix-services-start: ## Start PostgreSQL and Redis (Nix)
	./scripts/nix-services.sh start

nix-services-stop: ## Stop PostgreSQL and Redis (Nix)
	./scripts/nix-services.sh stop

nix-services-status: ## Check service status (Nix)
	./scripts/nix-services.sh status

# Service URL Management
service-urls-list: ## List all service URLs
	$(POETRY) run python scripts/service-urls.py list-services

service-urls-health: ## Check health of all services
	$(POETRY) run python scripts/service-urls.py health-check

service-urls-test: ## Test connectivity to all services
	$(POETRY) run python scripts/service-urls.py test-endpoints

clean: ## Clean build artifacts
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.coverage" -delete
	rm -rf .coverage
	rm -rf htmlcov
	rm -rf .pytest_cache
	rm -rf .mypy_cache
	rm -rf .ruff_cache
	rm -rf docs/build

docker-up: ## Start Docker services
	docker-compose -f docker/docker-compose.yml up -d

docker-down: ## Stop Docker services
	docker-compose -f docker/docker-compose.yml down

docker-logs: ## View Docker logs
	docker-compose -f docker/docker-compose.yml logs -f

db-init: ## Initialize database with Alembic
	$(POETRY) run alembic init alembic

db-migrate: ## Create new migration
	@test -n "$(msg)" || (echo "Error: msg parameter required. Usage: make db-migrate msg='your message'" && exit 1)
	$(POETRY) run alembic revision --autogenerate -m "$(msg)"

db-upgrade: ## Run database migrations
	@echo "🗄️ Running database migrations..."
	@$(NIX_SHELL) "alembic upgrade head"

db-downgrade: ## Rollback database migration
	@echo "🗄️ Rolling back database migration..."
	@$(NIX_SHELL) "alembic downgrade -1"

db-history: ## Show migration history
	@echo "🗄️ Database migration history:"
	@$(NIX_SHELL) "alembic history"

db-current: ## Show current database revision
	@echo "🗄️ Current database revision:"
	@$(NIX_SHELL) "alembic current"

db-reset: ## Reset database (drop all tables and re-run migrations)
	@echo "🗄️ Resetting database..."
	@$(NIX_SHELL) "alembic downgrade base && alembic upgrade head"

db-seed: ## Seed database with sample data
	@echo "🌱 Seeding database with sample data..."
	@$(NIX_SHELL) "python scripts/seed_database.py"

db-sqlite-upgrade: ## Run migrations with SQLite (for testing)
	DATABASE_BACKEND=sqlite $(POETRY) run alembic upgrade head

db-reset: ## Reset database (drop all tables and re-run migrations)
	$(POETRY) run alembic downgrade base
	$(POETRY) run alembic upgrade head

pre-commit: ## Run pre-commit hooks
	$(POETRY) run pre-commit run --all-files

install-hooks: ## Install git hooks (including work hours policy)
	$(POETRY) run pre-commit install
	./scripts/install-pre-push-hook.sh

# Traefik deployment targets
traefik-start: ## Start CSO Platform with Traefik proxy (*.cisocalc.localhost)
	@echo "🚀 Starting CSO Platform with Traefik..."
	./scripts/start-traefik.sh

traefik-stop: ## Stop Traefik deployment
	@echo "🛑 Stopping CSO Platform services..."
	cd docker && docker-compose down

traefik-logs: ## Show logs from Traefik deployment
	cd docker && docker-compose logs -f

traefik-status: ## Check status of Traefik services
	@echo "📊 CSO Platform Service Status:"
	@echo "================================"
	cd docker && docker-compose ps
	@echo ""
	@echo "🌐 Service Health Checks:"
	@curl -s http://api.cisocalc.localhost/health 2>/dev/null && echo "✅ API: Healthy" || echo "❌ API: Not responding"
	@curl -s http://app.cisocalc.localhost/health 2>/dev/null && echo "✅ Frontend: Healthy" || echo "❌ Frontend: Not responding"
	@curl -s http://docs.cisocalc.localhost 2>/dev/null && echo "✅ Docs: Healthy" || echo "❌ Docs: Not responding"

traefik-rebuild: ## Rebuild and restart Traefik deployment
	@echo "🔄 Rebuilding CSO Platform..."
	cd docker && docker-compose down
	cd docker && docker-compose up --build -d

# Testing with Traefik URLs
test-traefik: ## Run BDD tests against Traefik deployment
	TEST_BASE_URL=http://api.cisocalc.localhost $(POETRY) run behave features/ --no-capture

test-traefik-health: ## Test API health via Traefik
	TEST_BASE_URL=http://api.cisocalc.localhost $(POETRY) run behave features/api_health.feature --no-capture

# BDD Testing
test-behave: ## Run all BDD tests with current configuration
	$(POETRY) run behave features/ --no-capture

test-behave-tags: ## Run BDD tests with specific tags (usage: make test-behave-tags TAGS="@health_check,@api_docs")
	$(POETRY) run behave features/ --tags=$(TAGS) --no-capture
