# Quantitative Cybersecurity Decision Platform

**An integrated suite of financial modeling and reporting tools designed to empower Chief Security Officers (CSOs) and Chief Information Security Officers (CISOs) to translate complex security risks into clear financial metrics.**

🚀 **Transform abstract security concerns into concrete financial risk metrics** with automated, defensible financial models that enable CSOs to compete effectively for budget and demonstrate security's contribution to revenue enablement.

<div align="center">

![Python](https://img.shields.io/badge/python-v3.11+-blue.svg)
![FastAPI](https://img.shields.io/badge/FastAPI-0.109+-00a393.svg)
![SQLAlchemy](https://img.shields.io/badge/SQLAlchemy-2.0+-red.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)

[![CI](https://github.com/forkrul/cso-platform/workflows/CI/badge.svg)](https://github.com/forkrul/cso-platform/actions/workflows/ci.yml)
[![Documentation](https://github.com/forkrul/cso-platform/workflows/Documentation%20Check/badge.svg)](https://github.com/forkrul/cso-platform/actions/workflows/docs.yml)
[![codecov](https://codecov.io/gh/forkrul/cso-platform/branch/master/graph/badge.svg)](https://codecov.io/gh/forkrul/cso-platform)

[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Ruff](https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/astral-sh/ruff/main/assets/badge/v2.json)](https://github.com/astral-sh/ruff)
[![Pre-commit](https://img.shields.io/badge/pre--commit-enabled-brightgreen?logo=pre-commit&logoColor=white)](https://github.com/pre-commit/pre-commit)

[![Docker](https://img.shields.io/badge/docker-ready-blue?logo=docker)](https://www.docker.com/)
[![Nix](https://img.shields.io/badge/nix-supported-blue?logo=nixos)](https://nixos.org/)
[![Poetry](https://img.shields.io/badge/dependency-poetry-blue)](https://python-poetry.org/)

[![Security: bandit](https://img.shields.io/badge/security-bandit-yellow.svg)](https://github.com/PyCQA/bandit)
[![Checked with mypy](https://www.mypy-lang.org/static/mypy_badge.svg)](https://mypy-lang.org/)
[![Pydantic v2](https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/pydantic/pydantic/main/docs/badge/v2.json)](https://pydantic.dev)

[![FAIR](https://img.shields.io/badge/FAIR-compliant-green.svg)](https://www.fairinstitute.org/)
[![NIST CSF](https://img.shields.io/badge/NIST%20CSF-integrated-blue.svg)](https://www.nist.gov/cyberframework)
[![SOC2](https://img.shields.io/badge/SOC2-Type%20II-orange.svg)](https://www.aicpa.org/interestareas/frc/assuranceadvisoryservices/aicpasoc2report.html)

</div>

## 🎯 Vision

A single, integrated platform that replaces fragmented point solutions and manual spreadsheets with automated, defensible financial models that enable CSOs to compete effectively for budget, reduce personal liability through documented decision-making, and demonstrate security's contribution to revenue enablement.

## ✨ Core Modules

### 🎯 Module 1: Risk Exposure & Prioritization Calculator
- **FAIR Risk Quantification**: Full FAIR taxonomy implementation with Monte Carlo simulation
- **Asset Management**: Comprehensive asset inventory with multiple valuation methods
- **Threat Scenario Modeling**: Pre-built library of 50+ threat scenarios mapped to MITRE ATT&CK
- **Dynamic Risk Register**: Real-time ALE calculations with interactive heat maps
- **Control Effectiveness**: Mapped to NIST CSF, ISO 27001, and CIS frameworks

### 💰 Module 2: Security Investment Justification Console
- **ROSI Calculations**: Automated return on security investment modeling
- **TCO Analysis**: Comprehensive total cost of ownership with hidden cost discovery
- **Revenue Enablement**: Quantify security's contribution to sales and customer retention
- **Comparative Analysis**: Side-by-side vendor comparison with weighted scoring
- **Investment Portfolio**: Kanban board for tracking security investments

### 📊 Module 3: Dynamic Cybersecurity Budget Allocator
- **Risk-Based Allocation**: Automated optimization engine for budget distribution
- **Industry Benchmarking**: Real-time peer comparison across verticals and company sizes
- **Scenario Planning**: Multiple budget versions with impact analysis
- **Variance Tracking**: Historical budget analysis with trend identification

### 👥 Module 4: Security Team Staffing & Sourcing Modeler
- **Staffing Calculator**: Role-based headcount modeling with 24/7 coverage analysis
- **Cost Modeling**: Fully-loaded cost calculations including benefits and training
- **Outsourcing Analysis**: MSSP vs. in-house comparison with hybrid models
- **Skills Gap Analysis**: NICE Cybersecurity Workforce Framework integration

### ⚠️ Module 5: Cost of Inaction Simulator
- **Breach Cost Modeling**: Industry-specific scenarios with Ponemon/IBM integration
- **Regulatory Fine Calculator**: Multi-jurisdiction compliance with precedent database
- **Materiality Threshold**: SEC disclosure requirements with documentation generator
- **Business Impact Analysis**: Downtime costs and reputation impact scoring

### 📈 Module 6: C-Suite Reporting Dashboard
- **Executive Dashboards**: Pre-built templates for CEO, CFO, and Board presentations
- **Automated Reporting**: Scheduled reports in multiple formats (PDF, PPT, Excel)
- **NIST CSF Integration**: Automated scoring with maturity progression tracking
- **Data Storytelling**: Narrative generator with trend analysis and insights

## 🎯 Target Users

### Primary Personas

**Strategic CSO "Sarah"**
- Chief Security Officer at Fortune 1000 company
- 15+ years experience, reports to CEO
- Needs to justify $10M+ security budget to board
- Concerned about personal liability under SEC disclosure rules

**Operational CISO "Carlos"**
- CISO at mid-market financial services firm
- 10 years experience, reports to CIO
- Limited budget requires careful prioritization
- Deciding between in-house SOC vs. MSSP

**Risk-Focused VP "Victoria"**
- VP of Cybersecurity at healthcare organization
- 8 years experience, reports to CISO
- Managing 1000+ vulnerabilities with small team
- Needs to quantify risk for non-technical executives

## 🚀 Quick Start

### Current Status (Phase 1.3 Complete)

✅ **Enhanced Risk Calculations**: Monte Carlo simulations with 15/15 tests passing
✅ **ROI Analysis Engine**: Financial modeling with 7/7 tests passing
✅ **Monte Carlo Simulations**: Statistical risk modeling with 28/28 tests passing
✅ **Security Module**: Authentication and validation with 18/18 tests passing
✅ **User Service**: User management with 16/16 tests passing
✅ **Test Coverage**: 68% overall coverage with 94% pass rate (117/124 tests)
✅ **Documentation**: Comprehensive user guide, admin guide, and API documentation
✅ **Infrastructure**: Automated testing pipeline and service management

### Prerequisites

**Option 1: Nix (Recommended)**
- [Nix package manager](https://nixos.org/download.html)
- [direnv](https://direnv.net/) (optional but recommended)

**Option 2: Manual Setup**
- Python 3.11+
- Poetry
- PostgreSQL 16+ (for risk data and calculations)
- Redis (for caching and session management)
- Docker & Docker Compose (optional)

### Installation

#### Using Nix (Recommended)

1. **Clone the repository**
   ```bash
   git clone https://github.com/forkrul/cso-platform.git
   cd cso-platform
   ```

2. **Enter Nix development environment**
   ```bash
   # Traditional nix-shell (recommended)
   nix-shell

   # Or with direnv (automatic)
   direnv allow
   ```

3. **Start services and run automated setup**
   ```bash
   # Automated testing pipeline (starts services + runs tests)
   make test-auto

   # Or manual setup
   make start-services  # Start PostgreSQL and Redis
   make db-upgrade      # Run database migrations
   ```

4. **Start the development server**
   ```bash
   make dev
   ```

5. **Access the application**
   - **API Documentation**: http://localhost:8000/docs
   - **Alternative Docs**: http://localhost:8000/redoc
   - **Health Check**: http://localhost:8000/api/v1/health

#### Manual Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/forkrul/cso-platform.git
   cd cso-platform
   ```

2. **Install dependencies**
   ```bash
   make install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run database migrations**
   ```bash
   make db-upgrade
   ```

5. **Start the development server**
   ```bash
   make dev
   ```

6. **Visit the API documentation**
   - Swagger UI: http://localhost:8000/docs
   - ReDoc: http://localhost:8000/redoc

## Nix Development Environment

This project includes comprehensive Nix support for reproducible development environments:

### Nix Quick Start

```bash
# Clone project
git clone https://github.com/forkrul/8760.git
cd 8760

# Install Nix and direnv (one-time setup)
make install-nix

# Enter development environment
nix develop  # or nix-shell

# Install dependencies and start services
make install
services_start
make dev
```

**Alternative manual Nix installation:**
```bash
# Install Nix manually
sh <(curl -L https://nixos.org/nix/install) --daemon
```

### Nix Features

- **🔒 Reproducible**: Exact same environment for all developers
- **🚀 Fast Setup**: One command gets everything working
- **🗄️ Integrated Services**: PostgreSQL and Redis included
- **🛠️ Complete Toolchain**: Python, databases, docs, testing tools
- **🔄 Automatic**: Works with direnv for seamless development

### Nix Commands

```bash
# Service management (in nix-shell)
services_start     # Start PostgreSQL and Redis
services_stop      # Stop all services
services_status    # Check service status

# Or via scripts
make nix-services-start
make nix-services-stop
make nix-services-status
```

## Development

### Enhanced Make Commands

```bash
# 🚀 Service Management
make start-services      # Start PostgreSQL and Redis
make stop-services       # Stop all services
make services-status     # Check service status
make restart-services    # Restart all services

# 🧪 Testing (Comprehensive Suite)
make test-auto          # Automated pipeline: start services + run tests
make test               # Run all tests with coverage
make test-unit          # Run unit tests only
make test-integration   # Run integration tests only
make test-e2e           # Run end-to-end tests
make test-cov           # Detailed coverage report (HTML + XML)
make test-fast          # Quick test run without coverage
make test-ci            # CI/CD optimized test run

# 🔍 Code Quality
make lint               # Run linting checks (ruff + mypy)
make format             # Format code (ruff format)
make security           # Run security analysis (bandit)

# 🗄️ Database Management
make db-upgrade         # Run database migrations
make db-downgrade       # Rollback migration
make db-reset           # Reset database (drop + recreate)
make db-seed            # Seed with sample data
make db-history         # Show migration history
make db-current         # Show current revision

# 📚 Documentation
make docs               # Build documentation
make docs-serve         # Serve docs locally (port 8080)
make docs-clean         # Clean documentation build

# 🐳 Docker Operations
make docker-up          # Start Docker services
make docker-down        # Stop Docker services
```

### Quick Development Workflow

```bash
# 1. Start development environment
nix-shell
make start-services

# 2. Run automated tests to verify setup
make test-auto

# 3. Start development server
make dev

# 4. Make changes and test
make test-fast

# 5. Full validation before commit
make lint && make test-cov
```

### Using Docker

```bash
# Start all services
docker-compose -f docker/docker-compose.yml up -d

# View logs
docker-compose -f docker/docker-compose.yml logs -f

# Stop services
docker-compose -f docker/docker-compose.yml down
```

## 📁 Project Structure

```
cso-platform/                  # Project root
├── .github/workflows/          # GitHub Actions CI/CD
├── src/cso_platform/           # Main application code
│   ├── api/                    # API routes and endpoints
│   │   ├── v1/                 # API version 1
│   │   │   ├── risk/           # Risk quantification endpoints
│   │   │   ├── investment/     # Investment justification endpoints
│   │   │   ├── budget/         # Budget allocation endpoints
│   │   │   ├── staffing/       # Staffing model endpoints
│   │   │   ├── simulation/     # Cost of inaction endpoints
│   │   │   └── reporting/      # Dashboard and reporting endpoints
│   ├── core/                   # Core configuration and utilities
│   │   ├── config.py           # Application configuration
│   │   ├── database.py         # Database connection
│   │   ├── security.py         # Authentication and authorization
│   │   └── fair_engine.py      # FAIR risk calculation engine
│   ├── models/                 # SQLAlchemy models
│   │   ├── risk.py             # Risk and threat models
│   │   ├── asset.py            # Asset inventory models
│   │   ├── investment.py       # Investment tracking models
│   │   ├── budget.py           # Budget allocation models
│   │   ├── team.py             # Staffing and team models
│   │   └── report.py           # Reporting and dashboard models
│   ├── schemas/                # Pydantic schemas
│   │   ├── risk.py             # Risk calculation schemas
│   │   ├── investment.py       # Investment analysis schemas
│   │   ├── budget.py           # Budget planning schemas
│   │   └── reporting.py        # Dashboard and report schemas
│   ├── services/               # Business logic
│   │   ├── risk_calculator.py  # FAIR risk calculations
│   │   ├── rosi_analyzer.py    # ROSI and TCO analysis
│   │   ├── budget_optimizer.py # Budget allocation optimization
│   │   ├── staffing_modeler.py # Team sizing and cost modeling
│   │   ├── breach_simulator.py # Cost of inaction simulation
│   │   └── report_generator.py # Executive reporting
│   └── utils/                  # Utility functions
│       ├── monte_carlo.py      # Monte Carlo simulation engine
│       ├── benchmarks.py       # Industry benchmark data
│       └── integrations.py     # Third-party tool integrations
├── tests/                      # Test suite
│   ├── unit/                   # Unit tests for each module
│   ├── integration/            # Integration tests
│   └── e2e/                    # End-to-end workflow tests
├── docs/                       # Documentation
├── docker/                     # Docker configuration
├── alembic/                    # Database migrations
├── scripts/                    # Utility scripts
└── frontend/                   # React frontend (Next.js)
    ├── src/                    # Frontend source code
    │   ├── components/         # Reusable UI components
    │   ├── pages/              # Page components
    │   ├── hooks/              # Custom React hooks
    │   └── utils/              # Frontend utilities
    └── public/                 # Static assets
```

## Testing

The CSO Platform includes a comprehensive testing framework with **68% code coverage** and **94% test pass rate**:

### Test Results Summary

- **✅ Enhanced Risk Service**: 15/15 tests passing (Monte Carlo simulations, risk profiling)
- **✅ ROI Calculation Service**: 7/7 tests passing (NPV, IRR, payback period)
- **✅ Monte Carlo Simulations**: 28/28 tests passing (statistical modeling)
- **✅ Security Module**: 18/18 tests passing (authentication, validation)
- **✅ User Service**: 16/16 tests passing (user management)
- **📊 Overall**: 117/124 tests passing (94% pass rate)

### Test Categories

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test component interactions and API endpoints
- **E2E Tests**: Test complete user workflows with BDD (Behave)

### Testing Commands

```bash
# 🤖 Automated Testing Pipeline
make test-auto          # Start services + run comprehensive test suite

# 🧪 Test Execution
make test               # Run all tests with coverage
make test-cov           # Detailed coverage report (HTML + XML)
make test-fast          # Quick test run without coverage
make test-unit          # Unit tests only
make test-integration   # Integration tests only
make test-e2e           # End-to-end tests with Behave

# 🎯 Specific Testing
make test-specific FILE=test_enhanced_risk_service.py  # Run specific test file
```

### Test Coverage Goals

- **Overall Target**: 80% code coverage
- **Current Coverage**: 68%
- **Critical Modules**: 90%+ coverage required
  - Enhanced Risk Service: 66% (improving)
  - ROI Calculation Service: 44% (improving)
  - Monte Carlo Utils: 93% ✅
  - Security Module: 100% ✅

## Database

### Migrations

```bash
# Create a new migration
make db-migrate msg="Add user table"

# Apply migrations
make db-upgrade

# Rollback migration
make db-downgrade
```

### Models

All models inherit from `BaseModel` which provides:
- Automatic timestamps (`created_at`, `updated_at`)
- Soft delete functionality (`deleted_at`, `is_deleted`)
- Standard primary key (`id`)

## API Documentation

The CSO Platform provides comprehensive API documentation with enhanced risk calculation examples:

### Interactive Documentation
- **Swagger UI**: http://localhost:8000/docs (interactive testing)
- **ReDoc**: http://localhost:8000/redoc (clean documentation)
- **OpenAPI JSON**: http://localhost:8000/openapi.json (machine-readable spec)

### Key API Endpoints

#### Enhanced Risk Calculations
- `POST /api/v1/enhanced-risk/risk-profiles` - Create risk profiles
- `GET /api/v1/enhanced-risk/risk-profiles` - List risk profiles
- `POST /api/v1/enhanced-risk/calculations` - Run Monte Carlo simulations
- `GET /api/v1/enhanced-risk/calculations/{id}` - Get calculation results

#### ROI Calculations
- `POST /api/v1/calculations` - Create ROI calculations
- `GET /api/v1/calculations` - List calculations
- `GET /api/v1/calculations/{id}/summary` - Get detailed financial analysis

#### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User authentication
- `GET /api/v1/auth/profile` - Get user profile

### Example Usage

```bash
# Create a risk profile
curl -X POST "http://localhost:8000/api/v1/enhanced-risk/risk-profiles" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "name": "Healthcare Organization",
    "industry": "HEALTHCARE",
    "organization_size": "MEDIUM",
    "employee_count": 2500,
    "data_sensitivity_level": 4
  }'

# Run enhanced risk calculation with Monte Carlo simulation
curl -X POST "http://localhost:8000/api/v1/enhanced-risk/calculations" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "name": "Security Investment Analysis",
    "base_calculation_id": 123,
    "risk_profile_id": 456,
    "simulation_iterations": 10000
  }'
```

## Security

- JWT-based authentication
- Password hashing with bcrypt
- CORS configuration
- Rate limiting
- Input validation with Pydantic

## Documentation

The CSO Platform includes comprehensive documentation built with Sphinx:

### Available Documentation

- **📚 User Guide**: Complete guide for using enhanced risk calculations and ROI analysis
- **🔧 Admin Guide**: Deployment, configuration, monitoring, and troubleshooting
- **🔌 API Reference**: Detailed API documentation with examples
- **🧪 Test Documentation**: Test architecture, execution strategies, and coverage analysis
- **📋 Changelog**: Detailed history of all changes and improvements

### Documentation Commands

```bash
# Build documentation
make docs

# Serve documentation locally
make docs-serve  # Available at http://localhost:8080

# Clean documentation build
make docs-clean
```

### Documentation Structure

- **User Guide**: `/docs/source/user-guide/` - End-user documentation
- **Admin Guide**: `/docs/source/admin-guide/` - System administration
- **API Docs**: `/docs/source/api/` - API reference and examples
- **Test Docs**: `/tests/docs/` - Testing framework documentation

### Documentation Workflow

```bash
# 1. Setup GitHub Pages (one-time)
make setup-github-pages

# 2. Build documentation locally
make docs

# 3. Preview documentation
make docs-serve

# 4. Deploy to GitHub Pages
make docs-deploy
```

### GitHub Pages API Management

GitHub Pages can be configured programmatically:

```bash
# Enable GitHub Pages via script
./scripts/setup-github-pages.sh

# Or manually via API
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  https://api.github.com/repos/OWNER/REPO/pages \
  -d '{"source": {"branch": "gh-pages", "path": "/"}}'
```

## Deployment

### Production Docker

```bash
# Build production image
docker build -f docker/Dockerfile -t 8760:latest .

# Run production container
docker run -p 8000:8000 8760:latest
```

### Environment Variables

Key environment variables (see `.env.example`):

- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string
- `SECRET_KEY`: Application secret key
- `JWT_SECRET_KEY`: JWT signing key
- `ENVIRONMENT`: Environment (development/testing/production)

## 🎯 Current Implementation Status

### ✅ Phase 1.3 Complete (Enhanced Risk & Cost Modeling)

**Core Features Implemented:**
- **Enhanced Risk Calculations**: Monte Carlo simulations with statistical modeling
- **Risk Profile Management**: Industry-specific risk assessment with customizable parameters
- **ROI Analysis Engine**: NPV, IRR, payback period calculations
- **Financial Modeling**: Multi-year cost-benefit analysis with risk adjustments
- **Async Database Architecture**: High-performance PostgreSQL with asyncpg
- **Comprehensive API**: RESTful endpoints for all calculation types
- **Test Coverage**: 68% overall coverage with 94% pass rate

**Technical Achievements:**
- **Database**: Async PostgreSQL operations with connection pooling
- **Testing**: 117/124 tests passing with automated test pipeline
- **Documentation**: Complete user guide, admin guide, and API documentation
- **Infrastructure**: Service management and automated testing with Makefile
- **Security**: JWT authentication, input validation, and audit logging

### 🚧 Next Development Phases

**Phase 2.1: Advanced Financial Modeling**
- Industry benchmarking and peer comparison
- Scenario planning with multiple budget versions
- Advanced Monte Carlo parameter optimization
- Regulatory compliance cost modeling

**Phase 2.2: Reporting & Visualization**
- Executive dashboard with C-suite templates
- Automated report generation (PDF, PPT, Excel)
- Data visualization with interactive charts
- NIST CSF integration and maturity tracking

**Phase 2.3: Integration & Automation**
- Third-party tool integrations (SIEM, vulnerability scanners)
- Automated data ingestion and risk updates
- API integrations with security platforms
- Real-time risk monitoring and alerting

### 🎯 Getting Started for Contributors

```bash
# 1. Clone and setup
git clone https://github.com/forkrul/cso-platform.git
cd cso-platform
nix-shell

# 2. Run automated test suite
make test-auto

# 3. Start development
make dev

# 4. Make changes and validate
make lint && make test-cov
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests and linting (`make test-auto && make lint`)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Submit a pull request

### Development Guidelines

- **Test Coverage**: Maintain 80%+ coverage for new code
- **Documentation**: Update relevant documentation for new features
- **Code Quality**: Follow ruff formatting and pass mypy type checking
- **Security**: Run security analysis with `make security`

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**🎯 Ready to transform cybersecurity decision-making with quantitative risk analysis?**

Start with `make test-auto` to validate your setup, then `make dev` to begin development!
