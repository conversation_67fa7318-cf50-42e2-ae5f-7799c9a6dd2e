# Changelog

All notable changes to the CSO Platform project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Enhanced Risk Calculation Service with Monte Carlo simulations
- Comprehensive risk profiling with industry-specific parameters
- Advanced ROI calculations with NPV, IRR, and payback period analysis
- Monte Carlo simulation engine with multiple distribution types
- Async database operations with PostgreSQL and asyncpg driver
- Comprehensive test suite with 68% code coverage
- Test automation pipeline with service management
- Enhanced Sphinx documentation with user guide and admin guide
- Improved Makefile with service management and testing automation

### Changed
- Updated database configuration to use async PostgreSQL driver
- Migrated from synchronous to asynchronous service methods
- Enhanced test fixtures for async database operations
- Improved error handling and validation across all services
- Updated API documentation with enhanced risk calculation examples

### Fixed
- Resolved async/await issues in Enhanced Risk Service methods
- Fixed database connection issues with proper asyncpg configuration
- Corrected test mocking for AsyncSession compatibility
- Fixed Monte Carlo simulation integration and parameter validation
- Resolved data type conversion issues in ROI calculations
- Fixed calculation summary context setup issues
- Corrected base calculation linking and ID validation

### Security
- Enhanced JWT token validation and security
- Improved password hashing with proper salt generation
- Added comprehensive input validation and sanitization
- Implemented proper async session management

## [1.3.0] - 2024-01-15

### Added
- **Enhanced Risk Calculations**: Complete Monte Carlo simulation framework
  - Risk profile management with industry-specific parameters
  - Advanced statistical modeling with multiple distribution types
  - Confidence interval calculations and sensitivity analysis
  - Value at Risk (VaR) and Conditional VaR calculations
- **ROI Analysis Engine**: Comprehensive financial modeling
  - Net Present Value (NPV) calculations
  - Internal Rate of Return (IRR) analysis
  - Payback period calculations
  - Multi-year cost-benefit projections
- **Monte Carlo Simulation Service**: Statistical risk modeling
  - Support for Normal, Log-normal, Uniform, Triangular, and Beta distributions
  - Configurable simulation parameters and iterations
  - Performance optimization for large-scale simulations
- **Async Database Architecture**: High-performance data operations
  - PostgreSQL with asyncpg driver for optimal performance
  - Async SQLAlchemy 2.0 integration
  - Connection pooling and transaction management
- **Comprehensive Test Suite**: 94% test pass rate with 68% coverage
  - 117 passing tests across unit, integration, and E2E categories
  - Enhanced Risk Service: 15/15 tests passing ✅
  - ROI Calculation Service: 7/7 tests passing ✅
  - Monte Carlo Simulations: 28/28 tests passing ✅
  - Security Module: 18/18 tests passing ✅
  - User Service: 16/16 tests passing ✅

### Enhanced Features
- **Risk Profiling**: Industry-specific risk assessment
  - Healthcare, Financial, Technology, Manufacturing sectors
  - Organization size-based risk factors
  - Data sensitivity level classification
  - Security maturity assessment
- **Financial Modeling**: Advanced calculation capabilities
  - Time value of money calculations
  - Risk-adjusted return analysis
  - Scenario planning and sensitivity analysis
  - Compliance cost modeling
- **API Endpoints**: RESTful API for all calculations
  - `/api/v1/enhanced-risk/risk-profiles` - Risk profile management
  - `/api/v1/enhanced-risk/calculations` - Enhanced risk calculations
  - `/api/v1/calculations` - ROI calculations
  - `/api/v1/calculations/{id}/summary` - Calculation summaries

### Technical Improvements
- **Database Performance**: Async operations with connection pooling
- **Error Handling**: Comprehensive exception management
- **Validation**: Pydantic v2 schemas with advanced validation
- **Logging**: Structured logging with correlation IDs
- **Documentation**: Auto-generated API docs with examples

### Infrastructure
- **Nix Development Environment**: Reproducible development setup
- **Docker Support**: Containerized deployment options
- **Traefik Integration**: Reverse proxy with SSL termination
- **Service Management**: Automated service startup and health checks
- **CI/CD Pipeline**: Automated testing and deployment

## [1.2.0] - 2024-01-01

### Added
- Basic ROI calculation framework
- User authentication and authorization
- PostgreSQL database integration
- FastAPI application structure
- Basic test framework setup

### Changed
- Migrated from SQLite to PostgreSQL
- Updated authentication to use JWT tokens
- Improved API response formatting

### Fixed
- Database connection stability issues
- Authentication token expiration handling
- API endpoint validation errors

## [1.1.0] - 2023-12-15

### Added
- Initial project structure
- Basic FastAPI application
- User management system
- SQLite database integration
- Docker containerization

### Security
- JWT-based authentication
- Password hashing with bcrypt
- CORS configuration
- Input validation

## [1.0.0] - 2023-12-01

### Added
- Initial release of CSO Platform
- Basic web application framework
- User registration and login
- Health check endpoints
- Documentation structure

---

## Migration Notes

### Upgrading to 1.3.0

1. **Database Migration**: Run `make db-upgrade` to apply new schema changes
2. **Environment Variables**: Update `.env` file with new async database URL format
3. **Dependencies**: Run `make install` to update Python dependencies
4. **Services**: Use `make start-services` to start PostgreSQL and Redis
5. **Testing**: Run `make test-auto` to validate the upgrade

### Breaking Changes

- Database URLs must use `postgresql+asyncpg://` format for async operations
- Service methods are now async and require `await` keyword
- Test fixtures have been updated for async database operations

### Compatibility

- **Python**: Requires Python 3.11+
- **PostgreSQL**: Requires PostgreSQL 14+
- **Node.js**: Requires Node.js 18+ for frontend development
- **Docker**: Requires Docker 20.10+ for containerized deployment

## Support

For questions, issues, or contributions:

- **Documentation**: Check the comprehensive docs at `/docs`
- **API Reference**: Interactive API docs at `/docs` endpoint
- **Issues**: Report bugs and feature requests via GitHub Issues
- **Testing**: Run `make test-auto` for comprehensive validation

---

*This changelog is automatically updated with each release. For detailed commit history, see the Git log.*
